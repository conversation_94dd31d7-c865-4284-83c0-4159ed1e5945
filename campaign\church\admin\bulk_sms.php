<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get SMS settings
function getSMSSettings($pdo) {
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM sms_settings");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        return $result;
    } catch (PDOException $e) {
        return [];
    }
}

$smsSettings = getSMSSettings($pdo);
$smsEnabled = !empty($smsSettings['sms_enabled']) && $smsSettings['sms_enabled'] === '1';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_sms'])) {
    if (!$smsEnabled) {
        $error = "SMS service is not enabled. Please configure SMS settings first.";
    } else {
        try {
            $recipients = $_POST['recipients'] ?? '';
            $message = trim($_POST['message']);
            $templateId = intval($_POST['template_id'] ?? 0);
            $scheduleDate = $_POST['schedule_date'] ?? '';
            $scheduleTime = $_POST['schedule_time'] ?? '';
            
            if (empty($recipients)) {
                throw new Exception("Please select recipients.");
            }
            
            if (empty($message)) {
                throw new Exception("Please enter a message.");
            }
            
            // Validate message length (SMS limit is typically 160 characters)
            if (strlen($message) > 160) {
                $warning = "Message is " . strlen($message) . " characters. It may be sent as multiple SMS messages.";
            }
            
            // Get recipient list
            $recipientList = [];
            if ($recipients === 'all_members') {
                $stmt = $pdo->prepare("SELECT id, full_name, phone FROM members WHERE status = 'active' AND phone IS NOT NULL AND phone != ''");
                $stmt->execute();
                $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } elseif ($recipients === 'birthday_today') {
                $stmt = $pdo->prepare("SELECT id, full_name, phone FROM members WHERE status = 'active' AND phone IS NOT NULL AND phone != '' AND DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')");
                $stmt->execute();
                $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } elseif (strpos($recipients, 'group_') === 0) {
                $groupId = intval(str_replace('group_', '', $recipients));
                $stmt = $pdo->prepare("SELECT m.id, m.full_name, m.phone FROM members m JOIN contact_group_members cgm ON m.id = cgm.member_id WHERE cgm.group_id = ? AND m.status = 'active' AND m.phone IS NOT NULL AND m.phone != ''");
                $stmt->execute([$groupId]);
                $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            if (empty($recipientList)) {
                throw new Exception("No valid recipients found with phone numbers.");
            }
            
            // Schedule or send immediately
            $scheduledAt = null;
            if (!empty($scheduleDate) && !empty($scheduleTime)) {
                $scheduledAt = $scheduleDate . ' ' . $scheduleTime;
                if (strtotime($scheduledAt) <= time()) {
                    throw new Exception("Scheduled time must be in the future.");
                }
            }
            
            // Create SMS campaign
            $stmt = $pdo->prepare("INSERT INTO sms_campaigns (campaign_name, message, template_id, total_recipients, status, scheduled_at, created_by, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $campaignName = "Bulk SMS - " . date('Y-m-d H:i');
            $status = $scheduledAt ? 'scheduled' : 'sending';
            $stmt->execute([$campaignName, $message, $templateId > 0 ? $templateId : null, count($recipientList), $status, $scheduledAt, $_SESSION['admin_id']]);
            $campaignId = $pdo->lastInsertId();
            
            // Add recipients to campaign
            $stmt = $pdo->prepare("INSERT INTO sms_campaign_recipients (campaign_id, member_id, phone_number, status) VALUES (?, ?, ?, 'pending')");
            foreach ($recipientList as $recipient) {
                $stmt->execute([$campaignId, $recipient['id'], $recipient['phone']]);
            }
            
            if ($scheduledAt) {
                $success = "SMS campaign scheduled successfully for " . date('M j, Y g:i A', strtotime($scheduledAt)) . " with " . count($recipientList) . " recipients.";
            } else {
                // Send immediately (in a real implementation, this would be queued)
                $success = "SMS campaign created successfully with " . count($recipientList) . " recipients. Messages are being sent.";
                
                // Update campaign status
                $stmt = $pdo->prepare("UPDATE sms_campaigns SET status = 'completed', sent_at = NOW() WHERE id = ?");
                $stmt->execute([$campaignId]);
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get SMS templates
$smsTemplates = [];
try {
    $stmt = $pdo->prepare("SELECT id, template_name, message_content FROM sms_templates WHERE status = 'active' ORDER BY template_name");
    $stmt->execute();
    $smsTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Templates table might not exist yet
}

// Get contact groups
$contactGroups = [];
try {
    $stmt = $pdo->prepare("SELECT id, group_name, COUNT(cgm.member_id) as member_count FROM contact_groups cg LEFT JOIN contact_group_members cgm ON cg.id = cgm.group_id WHERE cg.status = 'active' GROUP BY cg.id ORDER BY cg.group_name");
    $stmt->execute();
    $contactGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Contact groups might not exist
}

// Get member counts
$memberCounts = [];
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_members FROM members WHERE status = 'active' AND phone IS NOT NULL AND phone != ''");
    $stmt->execute();
    $memberCounts['total'] = $stmt->fetch()['total_members'];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as birthday_today FROM members WHERE status = 'active' AND phone IS NOT NULL AND phone != '' AND DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')");
    $stmt->execute();
    $memberCounts['birthday'] = $stmt->fetch()['birthday_today'];
} catch (PDOException $e) {
    $memberCounts = ['total' => 0, 'birthday' => 0];
}

// Set page variables
$page_title = 'Bulk SMS';
$page_header = 'Bulk SMS';
$page_description = 'Send SMS messages to members and contacts.';

// Include header
include 'includes/header.php';
?>

<style>
.character-counter {
    font-size: 0.875rem;
    color: #6c757d;
}
.character-counter.warning {
    color: #fd7e14;
    font-weight: bold;
}
.character-counter.danger {
    color: #dc3545;
    font-weight: bold;
}
.recipient-info {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}
.sms-preview {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 1rem;
    border-radius: 0.375rem;
    font-family: monospace;
    white-space: pre-wrap;
}
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-chat-text-fill me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <a href="sms_campaigns.php" class="btn btn-outline-primary">
            <i class="bi bi-megaphone me-2"></i>View Campaigns
        </a>
        <a href="sms_templates.php" class="btn btn-outline-secondary">
            <i class="bi bi-file-text me-2"></i>Manage Templates
        </a>
        <a href="sms_integration.php" class="btn btn-outline-info">
            <i class="bi bi-gear me-2"></i>SMS Settings
        </a>
    </div>
</div>

<?php if (!$smsEnabled): ?>
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <strong>SMS Service Not Configured</strong><br>
        Please configure your SMS settings first to enable SMS functionality.
        <a href="sms_integration.php" class="btn btn-sm btn-outline-warning ms-2">Configure SMS</a>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
<?php endif; ?>

<?php if (isset($warning)): ?>
    <div class="alert alert-warning"><?php echo htmlspecialchars($warning); ?></div>
<?php endif; ?>

<div class="row">
    <!-- SMS Compose Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-pencil-square me-2"></i>Compose SMS
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="smsForm">
                    <!-- Recipients Selection -->
                    <div class="mb-3">
                        <label for="recipients" class="form-label">Recipients *</label>
                        <select class="form-select" id="recipients" name="recipients" required onchange="updateRecipientInfo()">
                            <option value="">Select Recipients</option>
                            <option value="all_members">All Members (<?php echo number_format($memberCounts['total']); ?> members)</option>
                            <?php if ($memberCounts['birthday'] > 0): ?>
                                <option value="birthday_today">Birthday Today (<?php echo number_format($memberCounts['birthday']); ?> members)</option>
                            <?php endif; ?>
                            <?php foreach ($contactGroups as $group): ?>
                                <option value="group_<?php echo $group['id']; ?>">
                                    <?php echo htmlspecialchars($group['group_name']); ?> (<?php echo number_format($group['member_count']); ?> members)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div id="recipientInfo" class="recipient-info" style="display: none;">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                <span id="recipientCount">0</span> recipients will receive this SMS
                            </small>
                        </div>
                    </div>

                    <!-- Template Selection -->
                    <?php if (!empty($smsTemplates)): ?>
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Use Template (Optional)</label>
                            <select class="form-select" id="template_id" name="template_id" onchange="loadTemplate()">
                                <option value="">Select a template...</option>
                                <?php foreach ($smsTemplates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>" data-content="<?php echo htmlspecialchars($template['message_content']); ?>">
                                        <?php echo htmlspecialchars($template['template_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>

                    <!-- Message Content -->
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="4" required 
                                  placeholder="Enter your SMS message..." onkeyup="updateCharacterCount()"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">SMS messages are typically limited to 160 characters</small>
                            <span id="characterCount" class="character-counter">0 characters</span>
                        </div>
                    </div>

                    <!-- Scheduling Options -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="scheduleMessage" onchange="toggleScheduling()">
                            <label class="form-check-label" for="scheduleMessage">
                                Schedule for later
                            </label>
                        </div>
                    </div>

                    <div id="schedulingOptions" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="schedule_date" class="form-label">Schedule Date</label>
                                <input type="date" class="form-control" id="schedule_date" name="schedule_date" 
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="schedule_time" class="form-label">Schedule Time</label>
                                <input type="time" class="form-control" id="schedule_time" name="schedule_time">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" name="send_sms" class="btn btn-primary btn-lg" <?php echo !$smsEnabled ? 'disabled' : ''; ?>>
                            <i class="bi bi-send me-2"></i>
                            <span id="submitButtonText">Send SMS Now</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- SMS Preview & Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-eye me-2"></i>SMS Preview
                </h5>
            </div>
            <div class="card-body">
                <div class="sms-preview" id="smsPreview">
                    Your SMS message will appear here...
                </div>
                
                <div class="mt-3">
                    <h6>SMS Guidelines:</h6>
                    <ul class="small text-muted">
                        <li>Keep messages under 160 characters for single SMS</li>
                        <li>Longer messages will be split into multiple SMS</li>
                        <li>Include clear call-to-action if needed</li>
                        <li>Avoid special characters that may not display properly</li>
                        <li>Test with a small group first for important campaigns</li>
                    </ul>
                </div>

                <?php if ($smsEnabled): ?>
                    <div class="mt-3 p-2 bg-light rounded">
                        <small class="text-success">
                            <i class="bi bi-check-circle me-1"></i>
                            SMS service is configured and ready
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo number_format($memberCounts['total']); ?></h4>
                            <small class="text-muted">Members with Phone</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning"><?php echo number_format($memberCounts['birthday']); ?></h4>
                        <small class="text-muted">Birthdays Today</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateCharacterCount() {
    const message = document.getElementById('message').value;
    const count = message.length;
    const counter = document.getElementById('characterCount');
    
    counter.textContent = count + ' characters';
    
    if (count > 160) {
        const smsCount = Math.ceil(count / 160);
        counter.textContent = count + ' characters (' + smsCount + ' SMS)';
        counter.className = 'character-counter danger';
    } else if (count > 140) {
        counter.className = 'character-counter warning';
    } else {
        counter.className = 'character-counter';
    }
    
    // Update preview
    document.getElementById('smsPreview').textContent = message || 'Your SMS message will appear here...';
}

function loadTemplate() {
    const select = document.getElementById('template_id');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value && selectedOption.dataset.content) {
        document.getElementById('message').value = selectedOption.dataset.content;
        updateCharacterCount();
    }
}

function updateRecipientInfo() {
    const select = document.getElementById('recipients');
    const info = document.getElementById('recipientInfo');
    const countSpan = document.getElementById('recipientCount');
    
    if (select.value) {
        const selectedText = select.options[select.selectedIndex].text;
        const match = selectedText.match(/\((\d+(?:,\d+)*)\s+members?\)/);
        
        if (match) {
            countSpan.textContent = match[1];
            info.style.display = 'block';
        }
    } else {
        info.style.display = 'none';
    }
}

function toggleScheduling() {
    const checkbox = document.getElementById('scheduleMessage');
    const options = document.getElementById('schedulingOptions');
    const submitText = document.getElementById('submitButtonText');
    
    if (checkbox.checked) {
        options.style.display = 'block';
        submitText.textContent = 'Schedule SMS';
    } else {
        options.style.display = 'none';
        submitText.textContent = 'Send SMS Now';
    }
}

// Initialize character count
document.addEventListener('DOMContentLoaded', function() {
    updateCharacterCount();
});
</script>

<?php include 'includes/footer.php'; ?>
