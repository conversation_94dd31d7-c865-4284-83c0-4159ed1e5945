<?php
// Include session manager for timeout handling
require_once __DIR__ . '/session-manager.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: " . admin_url_for('login.php'));
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="<?php echo get_organization_name(); ?> Management System v1.0">
    <!-- Debug info for path troubleshooting -->
    <meta name="debug-site-url" content="<?php echo SITE_URL; ?>">
    <meta name="debug-admin-url" content="<?php echo ADMIN_URL; ?>">
    <meta name="debug-env" content="<?php echo $environment ?? 'unknown'; ?>">
    <!-- Favicon Support -->
    <?php
    $favicon = get_site_setting('favicon_logo', '');
    if (!empty($favicon)): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url(); ?>/assets/images/favicon.ico">
    <?php endif; ?>

    <title><?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?> - <?php echo get_site_title(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Added cache-busting parameter to prevent caching issues -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/admin-css-proxy.php?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/font-fix.css?t=<?php echo time(); ?>">

    <!-- Custom Theme CSS -->
    <?php
    $customThemeFile = __DIR__ . '/../css/custom-theme.css';
    if (file_exists($customThemeFile)): ?>
        <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/custom-theme.css?t=<?php echo filemtime($customThemeFile); ?>">
    <?php endif; ?>

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
    
    <!-- Base URL for JavaScript -->
    <script>
        var BASE_URL = '<?php echo get_base_url(); ?>';
        var ADMIN_URL = '<?php echo get_admin_url(); ?>';
        var SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include the sidebar -->
            <?php include dirname(__FILE__) . '/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="main-content">
                <?php if (isset($page_header)): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2><?php echo $page_header; ?></h2>
                        <?php if (isset($page_description)): ?>
                            <p><?php echo $page_description; ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (isset($message) && !empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?> 