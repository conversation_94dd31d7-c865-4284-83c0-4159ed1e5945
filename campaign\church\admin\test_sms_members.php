<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>SMS Members Test</h2>";

try {
    // Test 1: All members with phone numbers
    echo "<h3>1. All Members with Phone Numbers</h3>";
    $stmt = $pdo->prepare("SELECT id, full_name, phone_number, date_of_birth, birth_date, status FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != ''");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Found " . count($members) . " members with phone numbers:</strong></p>";
    if (count($members) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Phone</th><th>Date of Birth</th><th>Birth Date</th><th>Status</th></tr>";
        foreach ($members as $member) {
            echo "<tr>";
            echo "<td>" . $member['id'] . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($member['phone_number']) . "</td>";
            echo "<td>" . ($member['date_of_birth'] ?? 'NULL') . "</td>";
            echo "<td>" . ($member['birth_date'] ?? 'NULL') . "</td>";
            echo "<td>" . $member['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 2: Birthday members today
    echo "<h3>2. Members with Birthdays Today</h3>";
    $stmt = $pdo->prepare("SELECT id, full_name, phone_number, date_of_birth, birth_date, 
                          DATE_FORMAT(date_of_birth, '%m-%d') as dob_md,
                          DATE_FORMAT(birth_date, '%m-%d') as bd_md,
                          DATE_FORMAT(NOW(), '%m-%d') as today_md
                          FROM members 
                          WHERE status = 'active' 
                          AND phone_number IS NOT NULL 
                          AND phone_number != '' 
                          AND (DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d') 
                               OR DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d'))");
    $stmt->execute();
    $birthdayMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Found " . count($birthdayMembers) . " members with birthdays today:</strong></p>";
    if (count($birthdayMembers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Phone</th><th>DOB</th><th>Birth Date</th><th>DOB M-D</th><th>BD M-D</th><th>Today M-D</th></tr>";
        foreach ($birthdayMembers as $member) {
            echo "<tr>";
            echo "<td>" . $member['id'] . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($member['phone_number']) . "</td>";
            echo "<td>" . ($member['date_of_birth'] ?? 'NULL') . "</td>";
            echo "<td>" . ($member['birth_date'] ?? 'NULL') . "</td>";
            echo "<td>" . ($member['dob_md'] ?? 'NULL') . "</td>";
            echo "<td>" . ($member['bd_md'] ?? 'NULL') . "</td>";
            echo "<td>" . $member['today_md'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><em>No members have birthdays today. Today is " . date('m-d') . "</em></p>";
        
        // Show some sample birthday dates for testing
        echo "<h4>Sample member birth dates for reference:</h4>";
        $stmt = $pdo->prepare("SELECT full_name, date_of_birth, birth_date, 
                              DATE_FORMAT(date_of_birth, '%m-%d') as dob_md,
                              DATE_FORMAT(birth_date, '%m-%d') as bd_md
                              FROM members 
                              WHERE status = 'active' 
                              AND phone_number IS NOT NULL 
                              AND phone_number != ''
                              LIMIT 5");
        $stmt->execute();
        $sampleMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sampleMembers) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Name</th><th>Date of Birth</th><th>Birth Date</th><th>DOB M-D</th><th>BD M-D</th></tr>";
            foreach ($sampleMembers as $member) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
                echo "<td>" . ($member['date_of_birth'] ?? 'NULL') . "</td>";
                echo "<td>" . ($member['birth_date'] ?? 'NULL') . "</td>";
                echo "<td>" . ($member['dob_md'] ?? 'NULL') . "</td>";
                echo "<td>" . ($member['bd_md'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Test 3: Contact Groups
    echo "<h3>3. Contact Groups with SMS-enabled Members</h3>";
    $stmt = $pdo->prepare("SELECT cg.id, cg.group_name, COUNT(cgm.member_id) as member_count 
                          FROM contact_groups cg 
                          LEFT JOIN contact_group_members cgm ON cg.id = cgm.group_id 
                          LEFT JOIN members m ON cgm.member_id = m.id 
                          WHERE cg.status = 'active' 
                          AND (m.phone_number IS NOT NULL AND m.phone_number != '' AND m.status = 'active') 
                          GROUP BY cg.id 
                          ORDER BY cg.group_name");
    $stmt->execute();
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Found " . count($groups) . " contact groups:</strong></p>";
    if (count($groups) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Group ID</th><th>Group Name</th><th>Members with Phone</th></tr>";
        foreach ($groups as $group) {
            echo "<tr>";
            echo "<td>" . $group['id'] . "</td>";
            echo "<td>" . htmlspecialchars($group['group_name']) . "</td>";
            echo "<td>" . $group['member_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><em>No contact groups found or no groups have members with phone numbers.</em></p>";
    }
    
    // Test 4: Current date info
    echo "<h3>4. Current Date Information</h3>";
    echo "<p><strong>Current Date:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "<p><strong>Current Month-Day:</strong> " . date('m-d') . "</p>";
    echo "<p><strong>MySQL NOW():</strong> ";
    $stmt = $pdo->query("SELECT NOW() as current_time, DATE_FORMAT(NOW(), '%m-%d') as current_md");
    $result = $stmt->fetch();
    echo $result['current_time'] . " (M-D: " . $result['current_md'] . ")</p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Database Error: " . $e->getMessage() . "</div>";
}

echo "<br><br><a href='bulk_sms.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Back to Bulk SMS</a>";
?>
